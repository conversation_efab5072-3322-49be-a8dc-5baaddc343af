import * as React from 'react';
import { Table, Modal } from 'antd';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { ECBasicOption } from 'echarts/types/dist/shared';
import style from '../../index.module.less';

// 注册ECharts组件
echarts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

/**
 * 单品排行图表组件属性接口
 */
interface ProductRankingChartProps {
  // 不需要额外的props
}

/**
 * 产品排行数据定义
 */
interface ProductRankingData {
  key: string;
  rank: number;
  product: string;
  energy: number;
  percentage: number;
}

/**
 * 单品排行图表组件
 *
 * 显示各产品按能耗量排序的排行榜，使用垂直柱状图展示
 * 可以直观看出哪些产品是能耗大户，便于重点关注和优化
 * 点击"更多"按钮可查看详细的产品排行数据表格
 *
 * @param props - 组件属性
 * @returns 单品排行图表组件
 */
const ProductRankingChart: React.FC<ProductRankingChartProps> = () => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstance = React.useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  // 模拟详细数据 - 产品能耗排行（从高到低）
  const detailData: ProductRankingData[] = [
    { key: '1', rank: 1, product: '乙烯', energy: 2.67, percentage: 18.5 },
    { key: '2', rank: 2, product: '丙烯', energy: 2.45, percentage: 16.9 },
    { key: '3', rank: 3, product: '苯', energy: 2.38, percentage: 16.4 },
    { key: '4', rank: 4, product: '甲苯', energy: 2.23, percentage: 15.4 },
    { key: '5', rank: 5, product: '二甲苯', energy: 2.12, percentage: 14.6 },
    { key: '6', rank: 6, product: '丁二烯', energy: 1.98, percentage: 13.7 },
    { key: '7', rank: 7, product: '环氧乙烷', energy: 1.85, percentage: 12.8 },
    { key: '8', rank: 8, product: '聚乙烯', energy: 1.72, percentage: 11.9 },
    { key: '9', rank: 9, product: '聚丙烯', energy: 1.58, percentage: 10.9 },
  ];

  // 定义渐变色彩方案 - 从深红到浅绿的温度渐变（高能耗=热色，低能耗=冷色）
  const colorPalette: string[] = [
    '#d32f2f', // 深红色 - 第1名（最高能耗，最"热"）
    '#f57c00', // 橙色 - 第2名
    '#fbc02d', // 黄色 - 第3名
    '#689f38', // 黄绿色 - 第4名
    '#388e3c', // 绿色 - 第5名
    '#00796b', // 青绿色 - 第6名
    '#0097a7', // 青色 - 第7名
    '#1976d2', // 蓝色 - 第8名
    '#303f9f', // 深蓝色 - 第9名（最低能耗，最"冷"）
  ];

  // ECharts配置选项
  const chartOption: ECBasicOption = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params: any) => {
          const data = params[0];
          const rankData = detailData[data.dataIndex];
          return `
            ${data.name}<br/>
            排名: 第${rankData.rank}名<br/>
            单品能耗: ${data.value} tce/万元<br/>
            占比: ${rankData.percentage}%
          `;
        },
      },
      grid: {
        left: '2%',
        right: '2%',
        bottom: '3%',
        top: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        name: 'tce/万元',
        nameTextStyle: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
          formatter: '{value}',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#f5f5f5',
            type: 'dashed',
          },
        },
      },
      yAxis: {
        type: 'category',
        data: detailData.map((item) => item.product),
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
          interval: 0, // 强制显示所有标签
        },
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
        inverse: true, // 反转Y轴，让第一名在最上面
      },
      series: [
        {
          name: '单品能耗',
          type: 'bar',
          data: detailData.map((item) => item.energy),
          itemStyle: {
            color: (params: any) => {
              // 使用预定义的颜色方案，每个排名都有独特的颜色
              const dataIndex = params.dataIndex;
              return colorPalette[dataIndex] || '#1890ff'; // 如果超出范围则使用默认蓝色
            },
          },
          barWidth: '60%',
          label: {
            show: true,
            position: 'right', // 横向条形图标签在右侧
            formatter: (params: any) => {
              const rank = detailData[params.dataIndex].rank;
              return `第${rank}名`;
            },
            color: '#595959',
            fontSize: 10,
          },
        },
      ],
    }),
    [detailData, colorPalette],
  );

  // 初始化图表
  React.useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      chartInstance.current.setOption(chartOption);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  // 更新图表配置
  React.useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.setOption(chartOption);
    }
  }, [chartOption]);

  // 处理窗口大小变化
  React.useEffect(() => {
    const handleResize: () => void = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  /**
   * 处理"更多"按钮点击事件
   */
  const handleMoreClick: () => void = () => {
    setIsModalVisible(true);
  };

  /**
   * 处理Modal关闭事件
   */
  const handleModalClose: () => void = () => {
    setIsModalVisible(false);
  };

  // 详细数据表格列配置
  const columns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 60,
      render: (rank: number) => {
        return <span>{rank}</span>;
      },
    },
    {
      title: '产品名称',
      dataIndex: 'product',
      key: 'product',
    },
    {
      title: '单品能耗',
      dataIndex: 'energy',
      key: 'energy',
      render: (value: number) => `${value.toFixed(2)} tce/万元`,
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是产品能耗排行的详细数据，按单品能耗从高到低排序：
      </p>
      <Table columns={columns} dataSource={detailData} pagination={false} size="small" bordered />
    </div>
  );

  return (
    <>
      <div className={style['product-ranking-chart']}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>单品排行</div>
          <div
            className={style['chart-more']}
            onClick={handleMoreClick}
            onKeyDown={(e) => e.key === 'Enter' && handleMoreClick()}
            role="button"
            tabIndex={0}
          >
            更多
          </div>
        </div>

        {/* ECharts图表容器 */}
        <div ref={chartRef} className={style['chart-container']} />
      </div>

      {/* 详细数据Modal */}
      <Modal
        title="单品排行 - 详细数据"
        visible={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent}
      </Modal>
    </>
  );
};

export default ProductRankingChart;
