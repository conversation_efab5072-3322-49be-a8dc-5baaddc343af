import * as React from 'react';
import { Select, Tooltip } from 'antd';
import { YTHLocalization } from 'yth-ui';
import locales from '@/locales';
import style from './index.module.less';
// 导入图表组件
import {
  EnergyTrendChart, // 能耗趋势图表组件
  ProductEnergyChart, // 单品能耗图表组件
  EnergyCompositionChart, // 企业能源构成图表组件
  ProductRankingChart, // 单品排行图表组件
  EnergyStructureChart, // 能源结构图表组件
} from './components';
// 导入页面专用图标组件
import {
  EnergyConsumptionIcon,
  EnergyCostIcon,
  EnergyEfficiencyIcon,
  UnitEnergyConsumptionIcon,
} from './components/Icons';

// 时间维度类型
type TimeDimension = 'year' | 'month';

// 统计周期类型
type StatisticsPeriod = 'recent3Years' | 'recent12Months' | 'recent6Months';

// 趋势数据类型
interface TrendData {
  type: 'up' | 'down';
  value: string;
}

// 指标值类型
interface IndicatorValue {
  value: string;
  trend: TrendData;
}

// 指标数据类型
interface IndicatorData {
  id: number;
  value: string;
  unit: string;
  title: string;
  tooltip: string;
  trend: TrendData;
  icon: React.ReactNode;
}

/**
 * 统计分析
 * @returns React.ReactNode
 */
const StatisticalAnalysis: React.FC = () => {
  // 时间维度状态
  const [timeDimension, setTimeDimension] = React.useState<TimeDimension>('month');

  // 统计周期状态
  const [statisticsPeriod, setStatisticsPeriod] =
    React.useState<StatisticsPeriod>('recent12Months');

  // 时间维度选项
  const timeDimensionOptions: { value: TimeDimension; label: string }[] = [
    { value: 'year', label: '年度统计' },
    { value: 'month', label: '月度统计' },
  ];

  // 根据时间维度获取统计周期选项
  const getStatisticsPeriodOptions: {
    value: StatisticsPeriod;
    label: string;
  }[] = React.useMemo(() => {
    if (timeDimension === 'year') {
      return [{ value: 'recent3Years', label: '近3年' }];
    }
    return [
      { value: 'recent12Months', label: '近12个月' },
      { value: 'recent6Months', label: '近6个月' },
    ];
  }, [timeDimension]);

  // 处理时间维度变更
  const handleTimeDimensionChange: (value: TimeDimension) => void = (value) => {
    setTimeDimension(value);
    // 根据时间维度自动设置默认的统计周期
    if (value === 'year') {
      setStatisticsPeriod('recent3Years');
    } else {
      setStatisticsPeriod('recent12Months');
    }
    // 数据更新通过 useMemo 自动响应时间维度变化
  };

  // 处理统计周期变更
  const handleStatisticsPeriodChange: (value: StatisticsPeriod) => void = (value) => {
    setStatisticsPeriod(value);
    // 数据更新通过 useMemo 自动响应统计周期变化
  };

  // 指标卡片数据 - 使用 useMemo 实现数据动态变化
  const indicatorData: IndicatorData[] = React.useMemo(() => {
    // 根据时间维度和统计周期获取对应的数据
    const getIndicatorValues: () => {
      energyConsumption: IndicatorValue;
      energyCost: IndicatorValue;
      energyEfficiency: IndicatorValue;
      unitEnergyConsumption: IndicatorValue;
    } = () => {
      // 这里可以根据 timeDimension 和 statisticsPeriod 返回不同的数据
      // 目前先提供示例数据，后续可以接入真实的 API 数据

      if (timeDimension === 'year') {
        // 年度统计数据
        return {
          energyConsumption: { value: '16,608', trend: { type: 'up', value: '+3.2%' } },
          energyCost: { value: '10,704', trend: { type: 'down', value: '-1.8%' } },
          energyEfficiency: { value: '78.5', trend: { type: 'up', value: '+2.1%' } },
          unitEnergyConsumption: { value: '2.28', trend: { type: 'down', value: '-4.2%' } },
        };
      }

      // 月度统计数据 - 根据统计周期调整
      const baseValues: Record<
        'recent12Months' | 'recent6Months',
        {
          energyConsumption: IndicatorValue;
          energyCost: IndicatorValue;
          energyEfficiency: IndicatorValue;
          unitEnergyConsumption: IndicatorValue;
        }
      > = {
        recent12Months: {
          energyConsumption: { value: '1,384', trend: { type: 'up', value: '+5.6%' } },
          energyCost: { value: '892', trend: { type: 'down', value: '-2.3%' } },
          energyEfficiency: { value: '76.8', trend: { type: 'up', value: '+1.2%' } },
          unitEnergyConsumption: { value: '2.34', trend: { type: 'down', value: '-3.1%' } },
        },
        recent6Months: {
          energyConsumption: { value: '1,425', trend: { type: 'up', value: '+7.2%' } },
          energyCost: { value: '865', trend: { type: 'down', value: '-3.1%' } },
          energyEfficiency: { value: '77.2', trend: { type: 'up', value: '+1.8%' } },
          unitEnergyConsumption: { value: '2.31', trend: { type: 'down', value: '-3.8%' } },
        },
      };

      // 对于月度数据，根据统计周期返回对应数据
      if (statisticsPeriod === 'recent6Months') {
        return baseValues.recent6Months;
      }
      return baseValues.recent12Months; // 默认返回近12个月数据
    };

    const values = getIndicatorValues();
    const timeUnit: string = timeDimension === 'year' ? '年度' : '单月';

    return [
      {
        id: 1,
        value: values.energyConsumption.value,
        unit: '万tce',
        title: `总综合能耗（${timeUnit}）`,
        tooltip: `统计周期内，各${timeDimension === 'year' ? '年' : '月'}综合能耗的算术平均值`,
        trend: values.energyConsumption.trend,
        icon: <EnergyConsumptionIcon />,
      },
      {
        id: 2,
        value: values.energyCost.value,
        unit: '万tce',
        title: `平均综合能耗（${timeUnit}）`,
        tooltip: `统计周期内，各${timeDimension === 'year' ? '年' : '月'}能源采购成本的算术平均值`,
        trend: values.energyCost.trend,
        icon: <EnergyCostIcon />,
      },
      {
        id: 3,
        value: values.energyEfficiency.value,
        unit: '万tce',
        title: '峰值综合能耗',
        tooltip: '有效能源利用量占总能源投入量的百分比',
        trend: values.energyEfficiency.trend,
        icon: <EnergyEfficiencyIcon />,
      },
      {
        id: 4,
        value: values.unitEnergyConsumption.value,
        unit: '%',
        title: '波动幅度',
        tooltip: '每万元产值所消耗的标准煤当量',
        trend: values.unitEnergyConsumption.trend,
        icon: <UnitEnergyConsumptionIcon />,
      },
    ];
  }, [timeDimension, statisticsPeriod]); // 依赖项：当时间维度或统计周期变化时重新计算

  return (
    <div className={style['statistical-analysis']}>
      {/* 筛选与导出区（顶部） */}
      <div className={style['filter-export-section']}>
        {/* 左侧：时间维度和统计周期 */}
        <div className={style['filter-controls']}>
          <div className={style['time-dimension']}>
            {/* 时间维度选择 */}
            <span className={style.label}>时间维度：</span>
            <Select
              value={timeDimension}
              onChange={handleTimeDimensionChange}
              options={timeDimensionOptions}
              style={{ width: 120 }}
              size="middle"
            />
          </div>
          <div className={style['period-selection']}>
            {/* 统计周期选择 */}
            <span className={style.label}>统计周期：</span>
            <Select
              value={statisticsPeriod}
              onChange={handleStatisticsPeriodChange}
              options={getStatisticsPeriodOptions}
              style={{ width: 120 }}
              size="middle"
            />
          </div>
        </div>

        {/* 右侧：月报导出 */}
        <div className={style['export-button']}>
          {/* 月报导出按钮 */}
          <span>月报导出</span>
        </div>
      </div>

      {/* 关键指标展示区（顶部下方，卡片式） */}
      <div className={style['key-indicators-section']}>
        {indicatorData.map((indicator) => (
          <div key={indicator.id} className={style['indicator-card']}>
            {/* 右侧图标 */}
            <div className={style['card-icon']}>{indicator.icon}</div>

            {/* 最上层：核心数值 */}
            <div className={style['card-value']}>
              <span className={style['value-number']}>{indicator.value}</span>
              <span className={style['value-unit']}>{indicator.unit}</span>
            </div>

            {/* 中间层：指标定义 + 交互提示 */}
            <div className={style['card-title']}>
              <span className={style['title-text']}>{indicator.title}</span>
              <Tooltip title={indicator.tooltip} placement="top">
                <div className={style['title-tooltip']}>?</div>
              </Tooltip>
            </div>

            {/* 最下层：趋势对比 */}
            <div className={style['card-trend']}>
              <span
                className={indicator.trend.type === 'up' ? style['trend-up'] : style['trend-down']}
              >
                <span className={style['trend-arrow']}>
                  {indicator.trend.type === 'up' ? '↑' : '↓'}
                </span>
                <span className={style['trend-text']}>同比 {indicator.trend.value}</span>
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* 数据可视化区（中间，分两排） */}
      <div className={style['visualization-section']}>
        {/* 第一排：能耗趋势（折线）、单品能耗（柱状 + 平均线） */}
        <div className={style['chart-row']}>
          {/* 能耗趋势 */}
          <EnergyTrendChart />
          {/* 单品能耗 */}
          <ProductEnergyChart />
        </div>

        {/* 第二排：企业能源构成（堆叠柱）、单品排行（横向条）、能源结构（饼图） */}
        <div className={style['chart-row']}>
          {/* 企业能源构成 */}
          <EnergyCompositionChart />
          {/* 单品排行 */}
          <ProductRankingChart />
          {/* 能源结构 */}
          <EnergyStructureChart />
        </div>
      </div>
    </div>
  );
};

export default YTHLocalization.withLocal(
  StatisticalAnalysis,
  locales,
  YTHLocalization.getLanguage(),
);
